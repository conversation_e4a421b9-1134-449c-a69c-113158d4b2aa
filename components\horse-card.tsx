"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Clock, DollarSign, User, Phone, Calendar, Edit, Trash2, Star, CheckCircle, Heart } from "lucide-react"
import type { Horse } from "@/lib/supabase"

interface HorseCardProps {
  horse: Horse
  onEdit?: (horse: Horse) => void
  onDelete?: (horseId: string) => void
}

export function HorseCard({ horse, onEdit, onDelete }: HorseCardProps) {
  const [imageError, setImageError] = useState(false)

  const formatPrice = (price: number | null) => {
    if (!price) return "N/A"
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      <div
        className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity ${
          horse.is_sold
            ? "bg-gradient-to-r from-green-500 to-emerald-600"
            : "bg-gradient-to-r from-blue-500 to-purple-600"
        }`}
      />

      {/* Horse Image */}
      <div className="aspect-video relative overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
        {!imageError ? (
          <img
            src={`/placeholder_image.png?height=200&width=300&text=${horse.coat}+${horse.breed}+horse`}
            alt={horse.name}
            className="w-full h-full object-cover transition-transform group-hover:scale-105 duration-500"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                <Heart className="h-8 w-8 text-blue-500" />
              </div>
              <p className="font-semibold text-gray-700 text-lg">{horse.breed}</p>
              <p className="text-sm text-gray-500">{horse.coat}</p>
            </div>
          </div>
        )}

        {/* Status Badges */}
        <div className="absolute top-3 right-3 flex gap-2">
          <Badge
            className={`font-semibold shadow-lg ${
              horse.is_fully_trained
                ? "bg-green-500 hover:bg-green-600 text-white"
                : "bg-orange-500 hover:bg-orange-600 text-white"
            }`}
          >
            Level {horse.level}
          </Badge>
          {horse.is_sold && (
            <Badge className="bg-emerald-500 hover:bg-emerald-600 text-white font-semibold shadow-lg">SOLD</Badge>
          )}
        </div>

        {/* Training Status */}
        <div className="absolute top-3 left-3">
          {horse.is_fully_trained && (
            <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold shadow-lg">
              <Star className="h-3 w-3 mr-1" />
              Fully Trained
            </Badge>
          )}
        </div>
      </div>

      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl text-gray-900 font-bold">{horse.name}</CardTitle>
            <p className="text-sm text-gray-600 font-medium">
              {horse.breed} • {horse.coat}
            </p>
          </div>
          <div className="flex gap-1">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(horse)}
                className="h-8 w-8 p-0 hover:bg-blue-100 text-gray-500 hover:text-blue-600 rounded-lg"
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(horse.id)}
                className="h-8 w-8 p-0 hover:bg-red-100 text-gray-500 hover:text-red-600 rounded-lg"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Training Progress */}
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span className="font-medium text-gray-700">Training Progress</span>
            <span className="text-gray-600">
              {horse.trained_skills.length}/{horse.total_skills}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(horse.trained_skills.length / horse.total_skills) * 100}%` }}
            />
          </div>
        </div>

        {/* Skills */}
        {horse.trained_skills.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {horse.trained_skills.slice(0, 3).map((skill) => (
              <Badge key={skill} variant="outline" className="text-xs border-green-300 text-green-700 bg-green-50">
                <CheckCircle className="h-3 w-3 mr-1" />
                {skill}
              </Badge>
            ))}
            {horse.trained_skills.length > 3 && (
              <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 bg-gray-50">
                +{horse.trained_skills.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Customer Information */}
        {horse.is_sold && horse.customer_name && (
          <div className="bg-green-50 p-4 rounded-xl border border-green-200">
            <h4 className="font-semibold text-green-800 mb-2 flex items-center">
              <User className="h-4 w-4 mr-2" />
              Customer Information
            </h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center text-green-700">
                <User className="h-3 w-3 mr-2" />
                <span className="font-medium">{horse.customer_name}</span>
              </div>
              {horse.customer_contact && (
                <div className="flex items-center text-green-700">
                  <Phone className="h-3 w-3 mr-2" />
                  <span>{horse.customer_contact}</span>
                </div>
              )}
              {horse.sale_date && (
                <div className="flex items-center text-green-700">
                  <Calendar className="h-3 w-3 mr-2" />
                  <span>Sold: {formatDate(horse.sale_date)}</span>
                </div>
              )}
              {horse.sale_price && (
                <div className="flex items-center text-green-700 font-semibold">
                  <DollarSign className="h-3 w-3 mr-2" />
                  <span>{formatPrice(horse.sale_price)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Notes */}
        {horse.notes && (
          <div className="text-xs text-gray-600 bg-gray-50 p-3 rounded-lg border-l-4 border-blue-400">
            <strong>Notes:</strong> {horse.notes}
          </div>
        )}

        {/* Last Trained */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            Last trained: {formatDate(horse.last_trained)}
          </div>
          <div className="text-right">Added: {formatDate(horse.created_at)}</div>
        </div>

        {/* Action Button */}
        {!horse.is_sold && (
          <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg">
            Manage Training
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
