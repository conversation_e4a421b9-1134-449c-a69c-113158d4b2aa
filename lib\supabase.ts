import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database
export interface Horse {
  id: string
  user_id: string
  name: string
  breed: string
  coat: string
  level: number
  is_fully_trained: boolean
  trained_skills: string[]
  total_skills: number
  last_trained: string
  is_sold: boolean
  customer_name: string | null
  customer_contact: string | null
  sale_date: string | null
  sale_price: number | null
  notes: string | null
  created_at: string
  updated_at: string
}

export interface Profile {
  id: string
  discord_id: string
  username: string
  avatar_url: string
  created_at: string
}
