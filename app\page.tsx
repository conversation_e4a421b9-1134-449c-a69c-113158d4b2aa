"use client"

import { <PERSON>, <PERSON>, DogI<PERSON> as Horse, Award, Users, ArrowRight, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function LandingPage() {
  const { user, loading, signInWithDiscord } = useAuth()
  const router = useRouter()

  // Redirect to dashboard if already logged in
  useEffect(() => {
    if (user && !loading) {
      router.push("/dashboard")
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto animate-pulse">
              <Heart className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center animate-bounce">
              <Horse className="h-4 w-4 text-white" />
            </div>
          </div>
          <p className="text-2xl font-semibold text-gray-700 mb-2">Loading...</p>
          <p className="text-gray-500">Getting everything ready for you</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="container mx-auto px-6 py-20">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center space-x-4 mb-8">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                  <Heart className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -top-3 -right-3 w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                  <Horse className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>

            <h1 className="text-7xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
              Horsing with Hearts
            </h1>

            <p className="text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              The ultimate platform for horse trainers to manage their stable, track training progress, and build
              lasting relationships with customers in the Wild West.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                onClick={signInWithDiscord}
                disabled={loading}
                className="bg-[#5865F2] hover:bg-[#4752C4] text-white px-8 py-4 text-xl font-semibold shadow-xl rounded-2xl transition-all duration-300 hover:shadow-2xl hover:scale-105"
                size="lg"
              >
                <Shield className="h-6 w-6 mr-3" />
                {loading ? "Connecting..." : "Get Started with Discord"}
                <ArrowRight className="h-6 w-6 ml-3" />
              </Button>

              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                  <span>Free to use</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                  <span>Secure login</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-20">
            <Card className="bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600 opacity-0 group-hover:opacity-5 transition-opacity pointer-events-none" />
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Horse className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-2xl text-gray-900 mb-2">Smart Horse Management</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Track training progress, manage horse details, and monitor skill development with our intuitive
                  dashboard designed for modern horse trainers.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Individual horse profiles
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Training progress tracking
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Skill development monitoring
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-green-600 opacity-0 group-hover:opacity-5 transition-opacity pointer-events-none" />
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl text-gray-900 mb-2">Customer Management</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Keep detailed records of horse sales, customer information, and build lasting relationships with horse
                  enthusiasts in your community.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Customer contact management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Sales tracking & history
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Revenue analytics
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-purple-600 opacity-0 group-hover:opacity-5 transition-opacity pointer-events-none" />
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Award className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-2xl text-gray-900 mb-2">Real-time Analytics</CardTitle>
                <CardDescription className="text-gray-600 text-base leading-relaxed">
                  Get insights into your training business with detailed analytics, revenue tracking, and performance
                  metrics to grow your operation.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Live dashboard updates
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Performance metrics
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    Business insights
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Stats Section */}
          <div className="bg-white rounded-3xl shadow-2xl p-12 mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Trusted by Horse Trainers Worldwide</h2>
              <p className="text-xl text-gray-600">Join the growing community of professional horse trainers</p>
            </div>

            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-blue-600 mb-2">1,000+</div>
                <div className="text-gray-600">Active Trainers</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-green-600 mb-2">5,000+</div>
                <div className="text-gray-600">Horses Managed</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-purple-600 mb-2">10,000+</div>
                <div className="text-gray-600">Training Sessions</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-pink-600 mb-2">99.9%</div>
                <div className="text-gray-600">Uptime</div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white">
              <h2 className="text-4xl font-bold mb-4">Ready to Transform Your Horse Training Business?</h2>
              <p className="text-xl mb-8 opacity-90">
                Join thousands of horse trainers who trust Horsing with Hearts to manage their stables
              </p>

              <Button
                onClick={signInWithDiscord}
                disabled={loading}
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-xl font-semibold shadow-xl rounded-2xl transition-all duration-300 hover:shadow-2xl hover:scale-105"
                size="lg"
              >
                <Shield className="h-6 w-6 mr-3" />
                {loading ? "Connecting..." : "Start Your Journey Today"}
                <ArrowRight className="h-6 w-6 ml-3" />
              </Button>

              <p className="text-sm mt-4 opacity-75">No credit card required • Free forever • Secure Discord login</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-20">
        <div className="container mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center">
                <Horse className="h-3 w-3 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold">Horsing with Hearts</h3>
          </div>

          <p className="text-gray-400 mb-6">Built with ❤️ for the horse training community</p>

          <div className="flex justify-center space-x-8 text-sm text-gray-400">
            <a href="#" className="hover:text-white transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Terms of Service
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Support
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Contact
            </a>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-800 text-gray-500 text-sm">
            © 2024 Horsing with Hearts. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  )
}
