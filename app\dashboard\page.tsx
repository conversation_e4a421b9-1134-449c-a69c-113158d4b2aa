"use client"

import { useState, useEffect } from "react"
import {
  Plus,
  Search,
  Filter,
  Heart,
  LogOut,
  Users,
  DollarSign,
  Star,
  TrendingUp,
  DogIcon as Horse,
  Eye,
  Setting<PERSON>,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { AddHorseDialog } from "@/components/add-horse-dialog"
import { HorseCard } from "@/components/horse-card"
import { HorseViewer } from "@/components/horse-viewer"
import { useAuth } from "@/components/auth-provider"
import { supabase, type Horse as HorseType } from "@/lib/supabase"
import { useRouter } from "next/navigation"

export default function Dashboard() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const [horses, setHorses] = useState<HorseType[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedHorse, setSelectedHorse] = useState<HorseType | null>(null)
  const [isViewerOpen, setIsViewerOpen] = useState(false)
  const [profile, setProfile] = useState<any>(null)

  // Redirect to landing page if not logged in
  useEffect(() => {
    if (!user && !loading) {
      router.push("/")
    }
  }, [user, loading, router])

  // Fetch user profile and horses
  useEffect(() => {
    if (user) {
      fetchProfile()
      fetchHorses()
    }
  }, [user])

  const fetchProfile = async () => {
    if (!user) return
    const { data } = await supabase.from("profiles").select("*").eq("id", user.id).single()
    setProfile(data)
  }

  const fetchHorses = async () => {
    if (!user) return
    const { data, error } = await supabase
      .from("horses")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching horses:", error)
    } else {
      setHorses(data || [])
    }
  }

  const handleAddHorse = async (horseData: any) => {
    if (!user) return

    const { data, error } = await supabase
      .from("horses")
      .insert({
        user_id: user.id,
        name: horseData.name,
        breed: horseData.breed,
        coat: horseData.coat,
        level: horseData.level,
        is_fully_trained: horseData.isFullyTrained,
        trained_skills: [],
        total_skills: horseData.totalSkills,
        is_sold: horseData.isSold,
        customer_name: horseData.customerName || null,
        customer_contact: horseData.customerContact || null,
        sale_date: horseData.saleDate || null,
        sale_price: horseData.salePrice || null,
        notes: horseData.notes || null,
      })
      .select()
      .single()

    if (error) {
      console.error("Error adding horse:", error)
    } else {
      setHorses([data, ...horses])
    }
  }

  const handleViewHorse = (horse: HorseType) => {
    setSelectedHorse(horse)
    setIsViewerOpen(true)
  }

  const handleDeleteHorse = async (horseId: string) => {
    if (!user) return

    const { error } = await supabase.from("horses").delete().eq("id", horseId).eq("user_id", user.id)

    if (error) {
      console.error("Error deleting horse:", error)
    } else {
      setHorses(horses.filter((h) => h.id !== horseId))
      setIsViewerOpen(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
    router.push("/")
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto animate-pulse">
              <Heart className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center animate-bounce">
              <Horse className="h-4 w-4 text-white" />
            </div>
          </div>
          <p className="text-2xl font-semibold text-gray-700 mb-2">Loading your stable...</p>
          <p className="text-gray-500">Getting everything ready for you</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to landing page
  }

  const filteredHorses = horses.filter((horse) => {
    const matchesSearch =
      horse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      horse.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (horse.customer_name && horse.customer_name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesFilter =
      filterStatus === "all" ||
      (filterStatus === "trained" && horse.is_fully_trained) ||
      (filterStatus === "training" && !horse.is_fully_trained) ||
      (filterStatus === "sold" && horse.is_sold) ||
      (filterStatus === "available" && !horse.is_sold)

    return matchesSearch && matchesFilter
  })

  const totalRevenue = horses.filter((h) => h.is_sold && h.sale_price).reduce((sum, h) => sum + (h.sale_price || 0), 0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Modern Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50 backdrop-blur-md bg-white/95">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center">
                  <Horse className="h-3 w-3 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Horsing with Hearts
                </h1>
                <p className="text-gray-600 text-sm">Premium Horse Training & Customer Management</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex items-center space-x-3 bg-gray-50 rounded-xl px-4 py-2 border border-gray-200 hover:bg-gray-100"
                  >
                    <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                      <AvatarImage src={profile?.avatar_url || user?.user_metadata?.avatar_url || "/placeholder.svg"} />
                      <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold">
                        {
                          (profile?.username ||
                            user?.user_metadata?.full_name ||
                            user?.user_metadata?.user_name ||
                            "U")[0]
                        }
                      </AvatarFallback>
                    </Avatar>
                    <div className="text-left">
                      <p className="font-semibold text-gray-900">
                        {profile?.username ||
                          user?.user_metadata?.full_name ||
                          user?.user_metadata?.user_name ||
                          "Horse Trainer"}
                      </p>
                      <p className="text-xs text-gray-500">Discord User</p>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 bg-white border-gray-200 shadow-xl rounded-xl" align="end">
                  <div className="px-3 py-2 border-b border-gray-100">
                    <p className="font-semibold text-gray-900">
                      {profile?.username ||
                        user?.user_metadata?.full_name ||
                        user?.user_metadata?.user_name ||
                        "Horse Trainer"}
                    </p>
                    <p className="text-sm text-gray-500">{user?.email}</p>
                    <p className="text-xs text-gray-400">
                      Discord ID: {profile?.discord_id || user?.user_metadata?.provider_id}
                    </p>
                  </div>
                  <DropdownMenuItem className="cursor-pointer">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={handleSignOut}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-6 py-8">
        {/* Modern Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600 opacity-0 group-hover:opacity-5 transition-opacity" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Horses</CardTitle>
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Horse className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{horses.length}</div>
              <p className="text-xs text-gray-500 mt-1">In your stable</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-green-600 opacity-0 group-hover:opacity-5 transition-opacity" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Fully Trained</CardTitle>
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Star className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{horses.filter((h) => h.is_fully_trained).length}</div>
              <p className="text-xs text-gray-500 mt-1">Ready for sale</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-orange-600 opacity-0 group-hover:opacity-5 transition-opacity" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">In Training</CardTitle>
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">
                {horses.filter((h) => !h.is_fully_trained && !h.is_sold).length}
              </div>
              <p className="text-xs text-gray-500 mt-1">Work in progress</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-purple-600 opacity-0 group-hover:opacity-5 transition-opacity" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Horses Sold</CardTitle>
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{horses.filter((h) => h.is_sold).length}</div>
              <p className="text-xs text-gray-500 mt-1">Happy customers</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-emerald-600 opacity-0 group-hover:opacity-5 transition-opacity" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">${totalRevenue.toLocaleString()}</div>
              <p className="text-xs text-gray-500 mt-1">From horse sales</p>
            </CardContent>
          </Card>
        </div>

        {/* Modern Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Search horses by name, breed, or customer..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500 shadow-sm rounded-xl h-12"
              />
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="border-gray-200 bg-white hover:bg-gray-50 text-gray-700 rounded-xl h-12 px-6 shadow-sm"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filter:{" "}
                {filterStatus === "all"
                  ? "All Horses"
                  : filterStatus === "available"
                    ? "Available"
                    : filterStatus === "training"
                      ? "In Training"
                      : filterStatus === "trained"
                        ? "Fully Trained"
                        : "Sold"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white border-gray-200 shadow-xl rounded-xl">
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>All Horses</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("available")}>Available</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("training")}>In Training</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("trained")}>Fully Trained</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("sold")}>Sold</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            onClick={() => setIsAddDialogOpen(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl h-12 px-6 shadow-lg font-semibold"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New Horse
          </Button>
        </div>

        {/* Horse Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredHorses.map((horse) => (
            <div key={horse.id} className="relative group">
              <HorseCard
                horse={horse}
                onEdit={(horse) => console.log("Edit horse:", horse)}
                onDelete={handleDeleteHorse}
              />
              <Button
                onClick={() => handleViewHorse(horse)}
                className="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity bg-white/90 hover:bg-white text-gray-700 shadow-lg rounded-lg p-2"
                size="sm"
              >
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        {/* Modern Empty State */}
        {filteredHorses.length === 0 && (
          <div className="text-center py-20">
            <div className="relative mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto shadow-lg">
                <Heart className="h-12 w-12 text-blue-500" />
              </div>
              <div className="absolute -top-2 -right-8 w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center shadow-lg">
                <Horse className="h-6 w-6 text-pink-500" />
              </div>
            </div>

            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              {searchTerm || filterStatus !== "all"
                ? "No horses match your search"
                : "Your stable is waiting for its first horse"}
            </h3>

            <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg">
              {searchTerm || filterStatus !== "all"
                ? "Try adjusting your search terms or filter settings to find the horses you're looking for."
                : "Start building your horse training business by adding your first horse to the stable. Every great trainer starts somewhere!"}
            </p>

            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl px-8 py-4 text-lg font-semibold shadow-lg"
              size="lg"
            >
              <Plus className="h-5 w-5 mr-2" />
              {horses.length === 0 ? "Add Your First Horse" : "Add New Horse"}
            </Button>
          </div>
        )}
      </main>

      <AddHorseDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} onAddHorse={handleAddHorse} />
      <HorseViewer
        horse={selectedHorse}
        open={isViewerOpen}
        onOpenChange={setIsViewerOpen}
        onEdit={(horse) => console.log("Edit horse:", horse)}
        onDelete={handleDeleteHorse}
      />
    </div>
  )
}
