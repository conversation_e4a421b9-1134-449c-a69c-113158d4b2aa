"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  Heart,
  Star,
  Clock,
  User,
  Phone,
  Calendar,
  DollarSign,
  Edit,
  Trash2,
  Activity,
  TrendingUp,
  Award,
} from "lucide-react"
import type { Horse } from "@/lib/supabase"
import { supabase } from "@/lib/supabase"

interface HorseViewerProps {
  horse: Horse | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onEdit?: (horse: Horse) => void
  onDelete?: (horseId: string) => void
}

export function HorseViewer({ horse, open, onOpenChange, onEdit, onDelete }: HorseViewerProps) {
  const [currentHorse, setCurrentHorse] = useState<Horse | null>(horse)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Real-time updates
  useEffect(() => {
    if (!horse) return

    // Set initial horse data
    setCurrentHorse(horse)
    setLastUpdated(new Date())

    // Subscribe to real-time changes
    const subscription = supabase
      .channel(`horse-${horse.id}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "horses",
          filter: `id=eq.${horse.id}`,
        },
        (payload) => {
          console.log("Real-time update:", payload)
          if (payload.new) {
            setCurrentHorse(payload.new as Horse)
            setLastUpdated(new Date())
          }
        },
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [horse])

  const formatPrice = (price: number | null) => {
    if (!price) return "N/A"
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString()
  }

  if (!currentHorse) return null

  const trainingProgress = (currentHorse.trained_skills.length / currentHorse.total_skills) * 100

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] bg-white max-h-[90vh] overflow-y-auto border-0 shadow-2xl">
        <DialogHeader className="text-center pb-6">
          <div className="relative mb-4">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <Heart className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -top-1 -right-6 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <Activity className="h-4 w-4 text-white" />
            </div>
          </div>
          <DialogTitle className="text-3xl font-bold text-gray-900">{currentHorse.name}</DialogTitle>
          <DialogDescription className="text-gray-600 text-lg">
            {currentHorse.breed} • {currentHorse.coat} • Real-time View
          </DialogDescription>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mt-2">
            <Activity className="h-4 w-4" />
            <span>Last updated: {formatTime(lastUpdated)}</span>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Horse Image and Status */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="aspect-video relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-100 to-purple-100">
                <img
                  src={`/placeholder_image.png?height=300&width=400&text=${currentHorse.coat}+${currentHorse.breed}+horse`}
                  alt={currentHorse.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.style.display = "none"
                    target.nextElementSibling?.classList.remove("hidden")
                  }}
                />
                <div className="hidden w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <Heart className="h-16 w-16 text-blue-500 mx-auto mb-3" />
                    <p className="font-semibold text-gray-700 text-xl">{currentHorse.breed}</p>
                    <p className="text-gray-500">{currentHorse.coat}</p>
                  </div>
                </div>

                {/* Status Badges */}
                <div className="absolute top-4 right-4 flex gap-2">
                  <Badge
                    className={`font-semibold shadow-lg ${
                      currentHorse.is_fully_trained
                        ? "bg-green-500 hover:bg-green-600 text-white"
                        : "bg-orange-500 hover:bg-orange-600 text-white"
                    }`}
                  >
                    Level {currentHorse.level}
                  </Badge>
                  {currentHorse.is_sold && (
                    <Badge className="bg-emerald-500 hover:bg-emerald-600 text-white font-semibold shadow-lg">
                      SOLD
                    </Badge>
                  )}
                </div>

                {currentHorse.is_fully_trained && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold shadow-lg">
                      <Star className="h-3 w-3 mr-1" />
                      Fully Trained
                    </Badge>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                {onEdit && (
                  <Button
                    onClick={() => onEdit(currentHorse)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white rounded-xl"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Horse
                  </Button>
                )}
                {onDelete && (
                  <Button
                    onClick={() => onDelete(currentHorse.id)}
                    variant="outline"
                    className="flex-1 border-red-200 text-red-600 hover:bg-red-50 rounded-xl"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
            </div>

            <div className="space-y-4">
              {/* Training Progress */}
              <Card className="bg-white border-0 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-gray-900">
                    <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                    Training Progress
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium text-gray-700">Overall Progress</span>
                      <span className="text-gray-600">
                        {currentHorse.trained_skills.length}/{currentHorse.total_skills} skills
                      </span>
                    </div>
                    <Progress value={trainingProgress} className="h-3" />
                    <p className="text-xs text-gray-500 mt-1">{Math.round(trainingProgress)}% complete</p>
                  </div>

                  {/* Skills */}
                  {currentHorse.trained_skills.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">Trained Skills</p>
                      <div className="flex flex-wrap gap-2">
                        {currentHorse.trained_skills.map((skill) => (
                          <Badge
                            key={skill}
                            className="bg-green-100 text-green-800 border border-green-300 hover:bg-green-200"
                          >
                            <Award className="h-3 w-3 mr-1" />
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Horse Details */}
              <Card className="bg-white border-0 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-gray-900">
                    <Heart className="h-5 w-5 mr-2 text-pink-600" />
                    Horse Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Breed</p>
                      <p className="font-semibold text-gray-900">{currentHorse.breed}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Coat Color</p>
                      <p className="font-semibold text-gray-900">{currentHorse.coat}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Current Level</p>
                      <p className="font-semibold text-gray-900">Level {currentHorse.level}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Status</p>
                      <p
                        className={`font-semibold ${
                          currentHorse.is_sold
                            ? "text-emerald-600"
                            : currentHorse.is_fully_trained
                              ? "text-green-600"
                              : "text-orange-600"
                        }`}
                      >
                        {currentHorse.is_sold
                          ? "Sold"
                          : currentHorse.is_fully_trained
                            ? "Fully Trained"
                            : "In Training"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Customer Information */}
          {currentHorse.is_sold && currentHorse.customer_name && (
            <Card className="bg-green-50 border border-green-200 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-green-800">
                  <User className="h-5 w-5 mr-2" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center text-green-700">
                      <User className="h-4 w-4 mr-2" />
                      <span className="font-semibold">{currentHorse.customer_name}</span>
                    </div>
                    {currentHorse.customer_contact && (
                      <div className="flex items-center text-green-700">
                        <Phone className="h-4 w-4 mr-2" />
                        <span>{currentHorse.customer_contact}</span>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    {currentHorse.sale_date && (
                      <div className="flex items-center text-green-700">
                        <Calendar className="h-4 w-4 mr-2" />
                        <span>Sold: {formatDate(currentHorse.sale_date)}</span>
                      </div>
                    )}
                    {currentHorse.sale_price && (
                      <div className="flex items-center text-green-700 font-semibold">
                        <DollarSign className="h-4 w-4 mr-2" />
                        <span>{formatPrice(currentHorse.sale_price)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {currentHorse.notes && (
            <Card className="bg-blue-50 border border-blue-200 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-blue-800">
                  <Heart className="h-5 w-5 mr-2" />
                  Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-blue-700">{currentHorse.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Timeline */}
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-gray-900">
                <Clock className="h-5 w-5 mr-2 text-purple-600" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Last trained</span>
                  <span className="font-semibold text-gray-900">{formatDate(currentHorse.last_trained)}</span>
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Added to stable</span>
                  <span className="font-semibold text-gray-900">{formatDate(currentHorse.created_at)}</span>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-gray-600">Last updated</span>
                  <span className="font-semibold text-gray-900">{formatDate(currentHorse.updated_at)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
