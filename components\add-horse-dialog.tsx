"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Heart, Star, User } from "lucide-react"

const horseBreeds = [
  "Arabian",
  "Mustang",
  "Thoroughbred",
  "American Standardbred",
  "Appaloosa",
  "Ardennes",
  "Belgian Draft",
  "Dutch Warmblood",
  "Hungarian Halfbred",
  "Kentucky Saddler",
  "Missouri Fox Trotter",
  "Nokota",
  "Shire",
  "Suffolk Punch",
  "Tennessee Walker",
  "Turkoman",
]

const coatColors = [
  "Black",
  "White",
  "Gray",
  "Bay",
  "Brown",
  "Chestnut",
  "Buckskin",
  "Palomino",
  "Pinto",
  "Roan",
  "Dapple Gray",
  "Cremello",
  "Champagne",
]

interface AddHorseDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAddHorse: (horse: any) => void
}

export function AddHorseDialog({ open, onOpenChange, onAddHorse }: AddHorseDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    breed: "",
    coat: "",
    level: 1,
    isFullyTrained: false,
    totalSkills: 6,
    isSold: false,
    customerName: "",
    customerContact: "",
    saleDate: "",
    salePrice: "",
    notes: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.name && formData.breed && formData.coat) {
      onAddHorse({
        ...formData,
        salePrice: formData.salePrice ? Number.parseFloat(formData.salePrice) : null,
      })
      setFormData({
        name: "",
        breed: "",
        coat: "",
        level: 1,
        isFullyTrained: false,
        totalSkills: 6,
        isSold: false,
        customerName: "",
        customerContact: "",
        saleDate: "",
        salePrice: "",
        notes: "",
      })
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto border-0 shadow-2xl">
        <DialogHeader className="text-center pb-4">
          <div className="relative mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <Heart className="h-8 w-8 text-white" />
            </div>
          </div>
          <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Add New Horse
          </DialogTitle>
          <DialogDescription className="text-gray-600 text-lg">
            Add a new horse to your stable with training and customer information.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-gray-100 rounded-xl">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white rounded-lg font-semibold">
                <Heart className="h-4 w-4 mr-2" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="training" className="data-[state=active]:bg-white rounded-lg font-semibold">
                <Star className="h-4 w-4 mr-2" />
                Training
              </TabsTrigger>
              <TabsTrigger value="customer" className="data-[state=active]:bg-white rounded-lg font-semibold">
                <User className="h-4 w-4 mr-2" />
                Customer
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-700 font-semibold">
                    Horse Name *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter horse name..."
                    className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="breed" className="text-gray-700 font-semibold">
                    Breed *
                  </Label>
                  <Select value={formData.breed} onValueChange={(value) => setFormData({ ...formData, breed: value })}>
                    <SelectTrigger className="border-gray-200 focus:border-blue-500 rounded-xl">
                      <SelectValue placeholder="Select breed..." />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl">
                      {horseBreeds.map((breed) => (
                        <SelectItem key={breed} value={breed}>
                          {breed}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="coat" className="text-gray-700 font-semibold">
                  Coat Color *
                </Label>
                <Select value={formData.coat} onValueChange={(value) => setFormData({ ...formData, coat: value })}>
                  <SelectTrigger className="border-gray-200 focus:border-blue-500 rounded-xl">
                    <SelectValue placeholder="Select coat color..." />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl">
                    {coatColors.map((coat) => (
                      <SelectItem key={coat} value={coat}>
                        {coat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="training" className="space-y-4 mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="level" className="text-gray-700 font-semibold">
                    Current Level
                  </Label>
                  <Input
                    id="level"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.level}
                    onChange={(e) => setFormData({ ...formData, level: Number.parseInt(e.target.value) || 1 })}
                    className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalSkills" className="text-gray-700 font-semibold">
                    Total Skills
                  </Label>
                  <Input
                    id="totalSkills"
                    type="number"
                    min="1"
                    max="20"
                    value={formData.totalSkills}
                    onChange={(e) => setFormData({ ...formData, totalSkills: Number.parseInt(e.target.value) || 6 })}
                    className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <Checkbox
                  id="isFullyTrained"
                  checked={formData.isFullyTrained}
                  onCheckedChange={(checked) => setFormData({ ...formData, isFullyTrained: !!checked })}
                />
                <Label htmlFor="isFullyTrained" className="text-blue-800 font-semibold">
                  Horse is fully trained
                </Label>
              </div>
            </TabsContent>

            <TabsContent value="customer" className="space-y-4 mt-6">
              <div className="flex items-center space-x-2 p-4 bg-green-50 rounded-xl border border-green-200">
                <Checkbox
                  id="isSold"
                  checked={formData.isSold}
                  onCheckedChange={(checked) => setFormData({ ...formData, isSold: !!checked })}
                />
                <Label htmlFor="isSold" className="text-green-800 font-semibold">
                  Horse is sold to a customer
                </Label>
              </div>

              {formData.isSold && (
                <div className="space-y-4 p-6 bg-green-50 rounded-xl border border-green-200">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="customerName" className="text-green-800 font-semibold">
                        Customer Name
                      </Label>
                      <Input
                        id="customerName"
                        value={formData.customerName}
                        onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                        placeholder="Enter customer name..."
                        className="border-green-300 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="customerContact" className="text-green-800 font-semibold">
                        Customer Contact
                      </Label>
                      <Input
                        id="customerContact"
                        value={formData.customerContact}
                        onChange={(e) => setFormData({ ...formData, customerContact: e.target.value })}
                        placeholder="Discord, phone, etc..."
                        className="border-green-300 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="saleDate" className="text-green-800 font-semibold">
                        Sale Date
                      </Label>
                      <Input
                        id="saleDate"
                        type="date"
                        value={formData.saleDate}
                        onChange={(e) => setFormData({ ...formData, saleDate: e.target.value })}
                        className="border-green-300 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="salePrice" className="text-green-800 font-semibold">
                        Sale Price ($)
                      </Label>
                      <Input
                        id="salePrice"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.salePrice}
                        onChange={(e) => setFormData({ ...formData, salePrice: e.target.value })}
                        placeholder="0.00"
                        className="border-green-300 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="notes" className="text-gray-700 font-semibold">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Additional notes about the horse..."
                  className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl min-h-[100px]"
                />
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-gray-200 hover:bg-gray-50 rounded-xl px-6"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl px-6 shadow-lg"
            >
              <Heart className="h-4 w-4 mr-2" />
              Add Horse to Stable
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
